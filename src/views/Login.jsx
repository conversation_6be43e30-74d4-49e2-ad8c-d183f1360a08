import React, { useState, useEffect, useRef } from "react";
import { withTranslation } from "react-i18next";
import {
  Box,
  Alert,
  Typography,
  TextField,
  Button,
  Grid,
  Link,
  Checkbox,
  FormControlLabel,
  IconButton,
  Card,
  Fade,
  CircularProgress,
} from "@mui/material";

import { useMutation, gql } from "@apollo/client";
import { ownerLogin } from "../apollo";
import { validateFunc } from "../constraints/constraints";
import LoginPageIcon from "../assets/img/LoginPageIcon.png";
import InputAdornment from "@mui/material/InputAdornment";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import EmailIcon from "@mui/icons-material/Email";
import LockIcon from "@mui/icons-material/Lock";

const LOGIN = gql`
  ${ownerLogin}
`;

// Professional design system for restaurant admin panel
const modernStyles = {
  loginContainer: {
    minHeight: "100vh",
    background: "linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)", // Professional gradient
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    padding: { xs: 3, sm: 4 },
    position: "relative",
    "&::before": {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background:
        "radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%)",
      pointerEvents: "none",
    },
  },
  loginCard: {
    background: "#ffffff",
    borderRadius: 4,
    boxShadow:
      "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    border: "1px solid rgba(226, 232, 240, 0.8)",
    overflow: "hidden",
    maxWidth: 1000,
    width: "100%",
    position: "relative",
    zIndex: 1,
  },
  leftPanel: {
    background: "linear-gradient(135deg, #1e293b 0%, #334155 100%)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    minHeight: { xs: 280, md: 600 },
    padding: { xs: 4, md: 6 },
    position: "relative",
    "&::before": {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background:
        "radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 70%)",
    },
  },
  rightPanel: {
    padding: { xs: 6, sm: 8, md: 10 },
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    minHeight: { xs: "auto", md: 600 },
    background: "#ffffff",
  },
  formContainer: {
    maxWidth: 400,
    margin: "0 auto",
    width: "100%",
  },
  brandSection: {
    textAlign: "center",
    marginBottom: 6,
  },
  logo: {
    width: 56,
    height: 56,
    borderRadius: 3,
    background: "linear-gradient(135deg, #f97316 0%, #ea580c 100%)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    margin: "0 auto 24px",
    color: "white",
    fontSize: "28px",
    fontWeight: "600",
    boxShadow: "0 10px 15px -3px rgba(249, 115, 22, 0.3)",
  },
  title: {
    fontWeight: 700,
    color: "#0f172a",
    marginBottom: 2,
    textAlign: "center",
    fontSize: { xs: "1.75rem", sm: "2rem" },
    letterSpacing: "-0.025em",
    lineHeight: 1.2,
  },
  subtitle: {
    color: "#475569",
    textAlign: "center",
    marginBottom: 5,
    fontSize: "0.9375rem",
    lineHeight: 1.5,
    fontWeight: 400,
  },
  textField: {
    marginBottom: 3,
    "& .MuiOutlinedInput-root": {
      borderRadius: 3,
      backgroundColor: "#f8fafc",
      border: "1.5px solid #e2e8f0",
      height: 56,
      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      "&:hover": {
        backgroundColor: "#f1f5f9",
        borderColor: "#cbd5e1",
        transform: "translateY(-1px)",
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
      },
      "&.Mui-focused": {
        backgroundColor: "#ffffff",
        borderColor: "#f97316",
        boxShadow: "0 0 0 4px rgba(249, 115, 22, 0.1)",
        transform: "translateY(-1px)",
      },
      "& fieldset": {
        border: "none",
      },
      "& input": {
        padding: "16px 14px",
        fontSize: "0.9375rem",
        fontWeight: 500,
        color: "#1e293b",
        "&::placeholder": {
          color: "#94a3b8",
          opacity: 1,
        },
      },
    },
    "& .MuiInputLabel-root": {
      color: "#64748b",
      fontSize: "0.9375rem",
      fontWeight: 500,
      transform: "translate(14px, 16px) scale(1)",
      "&.Mui-focused": {
        color: "#f97316",
      },
      "&.MuiInputLabel-shrink": {
        transform: "translate(14px, -9px) scale(0.75)",
        backgroundColor: "#ffffff",
        padding: "0 8px",
      },
    },
  },
  loginButton: {
    borderRadius: 3,
    padding: "16px 0",
    background: "linear-gradient(135deg, #f97316 0%, #ea580c 100%)",
    color: "white",
    fontWeight: 600,
    fontSize: "0.9375rem",
    textTransform: "none",
    boxShadow: "0 4px 6px -1px rgba(249, 115, 22, 0.4)",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    "&:hover": {
      background: "linear-gradient(135deg, #ea580c 0%, #dc2626 100%)",
      boxShadow: "0 10px 15px -3px rgba(249, 115, 22, 0.5)",
      transform: "translateY(-2px)",
    },
    "&:active": {
      transform: "translateY(0)",
    },
    "&:disabled": {
      background: "#e2e8f0",
      color: "#94a3b8",
      boxShadow: "none",
      transform: "none",
    },
  },
  forgotLink: {
    color: "#f97316",
    textDecoration: "none",
    fontWeight: 500,
    fontSize: "0.875rem",
    transition: "all 0.2s ease",
    "&:hover": {
      color: "#ea580c",
      textDecoration: "underline",
    },
  },
  rememberSection: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
    marginTop: 2,
  },
  illustration: {
    maxWidth: "85%",
    height: "auto",
    opacity: 0.95,
    filter: "brightness(1.1) contrast(1.05)",
  },
  leftContent: {
    textAlign: "center",
    maxWidth: 380,
    position: "relative",
    zIndex: 2,
  },
  leftTitle: {
    color: "#ffffff",
    fontWeight: 700,
    marginTop: 4,
    marginBottom: 2,
    fontSize: { xs: "1.5rem", md: "1.75rem" },
    letterSpacing: "-0.025em",
    lineHeight: 1.3,
    textShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
  },
  leftSubtitle: {
    color: "rgba(255, 255, 255, 0.9)",
    lineHeight: 1.6,
    fontSize: "1rem",
    fontWeight: 400,
    textShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
  },
  errorAlert: {
    marginBottom: 4,
    borderRadius: 3,
    backgroundColor: "#fef2f2",
    border: "1.5px solid #fecaca",
    color: "#dc2626",
    "& .MuiAlert-icon": {
      color: "#dc2626",
    },
    "& .MuiAlert-message": {
      fontSize: "0.875rem",
      fontWeight: 500,
    },
  },
  checkboxStyle: {
    color: "#f97316",
    "&.Mui-checked": {
      color: "#f97316",
    },
    "& .MuiSvgIcon-root": {
      fontSize: 20,
    },
  },
  helpText: {
    textAlign: "center",
    marginTop: 4,
    color: "#94a3b8",
    fontSize: "0.8125rem",
    fontWeight: 400,
    lineHeight: 1.4,
  },
};

const Login = (props) => {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [stateData, setStateData] = useState({
    email: "<EMAIL>",
    password: "123123",
    emailError: null,
    passwordError: null,
    error: null,
    type: null, /// 0 for vendor
    redirectToReferrer: !!localStorage.getItem("user-enatega"),
  });
  const formRef = useRef();

  const [isLogged, setIsLogged] = useState(false);

  const onBlur = (field) => {
    setStateData({
      ...stateData,
      [field + "Error"]: !validateFunc({ [field]: stateData[field] }, field),
    });
  };
  const validate = () => {
    const emailError = !validateFunc({ email: stateData.email }, "email");
    const passwordError = !validateFunc(
      { password: stateData.password },
      "password"
    );
    setStateData({ ...stateData, emailError, passwordError });
    return emailError && passwordError;
  };
  const { redirectToReferrer, type } = stateData;

  useEffect(() => {
    if (isLogged) {
      if (redirectToReferrer && type === 0) {
        props.history.replace("/restaurant/list");
      }
      if (redirectToReferrer && type === 1) {
        props.history.replace("/super_admin/vendors");
      }
    }
  }, [isLogged]);

  const onCompleted = (data) => {
    setLoading(false);
    localStorage.setItem("user-enatega", JSON.stringify(data.ownerLogin));
    const userType = data.ownerLogin.userType;
    if (userType === "VENDOR") {
      setStateData({
        ...stateData,
        redirectToReferrer: true,
        type: 0,
        emailError: null,
        passwordError: null,
        error: null,
      });
    } else if (userType === "ADMIN" || userType === "SUPER_ADMIN") {
      setStateData({
        ...stateData,
        redirectToReferrer: true,
        type: 1,
        emailError: null,
        passwordError: null,
        error: null,
      });
    }
    setIsLogged(true);
    setTimeout(hideAlert, 5000);
  };

  const hideAlert = () => {
    setStateData({
      ...stateData,
      emailError: null,
      passwordError: null,
      error: null,
    });
  };

  const onError = (error) => {
    setLoading(false);
    let errorMessage = "An error occurred. Please try again.";

    if (error.graphQLErrors.length) {
      errorMessage = error.graphQLErrors[0].message;
    } else if (error.networkError) {
      errorMessage = "Network error. Please check your connection.";
    }

    setStateData({
      ...stateData,
      error: errorMessage,
    });
    setIsLogged(false);
    setTimeout(hideAlert, 5000);
  };

  const [mutate] = useMutation(LOGIN, { onError, onCompleted });

  const loginFunc = async () => {
    if (validate()) {
      setLoading(true);
      mutate({
        variables: { email: stateData.email, password: stateData.password },
      });
    }
  };

  const handleInputChange = (field, value) => {
    setStateData({
      ...stateData,
      [field]: value,
      [field + "Error"]: null, // Clear error when user starts typing
    });
  };

  return (
    <Box sx={modernStyles.loginContainer}>
      <Fade in={true} timeout={800}>
        <Card sx={modernStyles.loginCard}>
          <Grid container sx={{ minHeight: { md: 600 } }}>
            {/* Left Panel - Illustration */}
            <Grid item xs={12} md={5}>
              <Box sx={modernStyles.leftPanel}>
                <Box sx={modernStyles.leftContent}>
                  <img
                    src={LoginPageIcon}
                    alt="Restaurant Admin Dashboard"
                    style={modernStyles.illustration}
                  />
                  <Typography variant="h4" sx={modernStyles.leftTitle}>
                    Restaurant Management System
                  </Typography>
                  <Typography variant="body1" sx={modernStyles.leftSubtitle}>
                    Streamline your restaurant operations with our comprehensive
                    admin dashboard. Manage orders, menu, staff, and analytics
                    all in one place.
                  </Typography>
                </Box>
              </Box>
            </Grid>

            {/* Right Panel - Form */}
            <Grid item xs={12} md={7}>
              <Box sx={modernStyles.rightPanel}>
                <Box sx={modernStyles.formContainer}>
                  {/* Brand Section */}
                  <Box sx={modernStyles.brandSection}>
                    <Box sx={modernStyles.logo}>�</Box>
                    <Typography variant="h3" sx={modernStyles.title}>
                      Welcome Back
                    </Typography>
                    <Typography variant="body1" sx={modernStyles.subtitle}>
                      Please sign in to access your admin dashboard
                    </Typography>
                  </Box>

                  {/* Error Alert */}
                  {stateData.error && (
                    <Alert
                      severity="error"
                      sx={{
                        marginBottom: 3,
                        borderRadius: 2,
                        backgroundColor: "#fef2f2",
                        border: "1px solid #fecaca",
                        color: "#dc2626",
                        "& .MuiAlert-icon": {
                          color: "#dc2626",
                        },
                        "& .MuiAlert-message": {
                          fontSize: "0.875rem",
                        },
                      }}
                    >
                      {stateData.error}
                    </Alert>
                  )}

                  <form
                    ref={formRef}
                    onSubmit={(e) => {
                      e.preventDefault();
                      loginFunc();
                    }}
                  >
                    {/* Email Field */}
                    <TextField
                      fullWidth
                      label="Email Address"
                      type="email"
                      value={stateData.email}
                      onChange={(e) =>
                        handleInputChange("email", e.target.value)
                      }
                      onBlur={() => onBlur("email")}
                      error={stateData.emailError === false}
                      helperText={
                        stateData.emailError === false
                          ? "Please enter a valid email address"
                          : ""
                      }
                      sx={modernStyles.textField}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon
                              sx={{ color: "#94a3b8", fontSize: 20 }}
                            />
                          </InputAdornment>
                        ),
                      }}
                      disabled={loading}
                      placeholder="Enter your email"
                    />

                    {/* Password Field */}
                    <TextField
                      fullWidth
                      label="Password"
                      type={showPassword ? "text" : "password"}
                      value={stateData.password}
                      onChange={(e) =>
                        handleInputChange("password", e.target.value)
                      }
                      onBlur={() => onBlur("password")}
                      error={stateData.passwordError === false}
                      helperText={
                        stateData.passwordError === false
                          ? "Password is required"
                          : ""
                      }
                      sx={modernStyles.textField}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LockIcon sx={{ color: "#94a3b8", fontSize: 20 }} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowPassword(!showPassword)}
                              edge="end"
                              disabled={loading}
                              sx={{
                                color: "#94a3b8",
                                "&:hover": {
                                  color: "#64748b",
                                  backgroundColor: "transparent",
                                },
                              }}
                            >
                              {showPassword ? (
                                <VisibilityOffIcon fontSize="small" />
                              ) : (
                                <VisibilityIcon fontSize="small" />
                              )}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      disabled={loading}
                      placeholder="Enter your password"
                    />
                    {/* Remember Me and Forgot Password */}
                    <Box sx={modernStyles.rememberSection}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            defaultChecked
                            sx={{
                              color: "#f97316",
                              "&.Mui-checked": {
                                color: "#f97316",
                              },
                              "& .MuiSvgIcon-root": {
                                fontSize: 18,
                              },
                            }}
                          />
                        }
                        label={
                          <Typography
                            variant="body2"
                            sx={{
                              color: "#64748b",
                              fontSize: "0.875rem",
                            }}
                          >
                            Remember me
                          </Typography>
                        }
                        disabled={loading}
                      />
                      <Link href="/#/auth/reset" sx={modernStyles.forgotLink}>
                        Forgot password?
                      </Link>
                    </Box>

                    {/* Login Button */}
                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      disabled={loading}
                      sx={modernStyles.loginButton}
                    >
                      {loading ? (
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: 1.5,
                            justifyContent: "center",
                          }}
                        >
                          <CircularProgress
                            size={18}
                            sx={{ color: "white" }}
                            thickness={4}
                          />
                          <span>Signing in...</span>
                        </Box>
                      ) : (
                        "Sign In"
                      )}
                    </Button>

                    {/* Additional Help */}
                    <Box sx={{ textAlign: "center", marginTop: 3 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "#94a3b8",
                          fontSize: "0.8125rem",
                        }}
                      >
                        Need help? Contact your system administrator
                      </Typography>
                    </Box>
                  </form>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Card>
      </Fade>
    </Box>
  );
};
export default withTranslation()(Login);
