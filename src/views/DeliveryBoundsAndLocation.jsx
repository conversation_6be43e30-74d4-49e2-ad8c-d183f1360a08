import React, { useCallback, useRef, useState } from "react";
import { <PERSON>Map, Marker, Polygon } from "@react-google-maps/api";
import { useMutation, useQuery, gql } from "@apollo/client";
import Header from "../components/Headers/Header";
import { transformPolygon, transformPath } from "../utils/coordinates";
import {
  updateDeliveryBoundsAndLocation,
  getRestaurantProfile,
} from "../apollo";
import CustomLoader from "../components/Loader/CustomLoader";
import {
  Container,
  Box,
  Button,
  Typography,
  Alert,
  useTheme,
  Card,
  CardContent,
  Chip,
  Fab,
  Snackbar,
  Paper,
  Stack,
  Divider,
} from "@mui/material";
import {
  LocationOn as LocationOnIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  MyLocation as MyLocationIcon,
  Polygon as PolygonIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from "@mui/icons-material";
import { useTranslation, withTranslation } from "react-i18next";

const UPDATE_DELIVERY_BOUNDS_AND_LOCATION = gql`
  ${updateDeliveryBoundsAndLocation}
`;
const GET_RESTAURANT_PROFILE = gql`
  ${getRestaurantProfile}
`;

function DeliveryBoundsAndLocation() {
  const { t } = useTranslation();
  const theme = useTheme();
  const restaurantId = localStorage.getItem("restaurantId");

  // UI State
  const [drawBoundsOrMarker, setDrawBoundsOrMarker] = useState("marker");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Map State
  const [mapLoaded, setMapLoaded] = useState(false);

  const [center, setCenter] = useState({ lat: 33.684422, lng: 73.047882 });
  const [marker, setMarker] = useState({ lat: 33.684422, lng: 73.047882 });
  const [path, setPath] = useState([
    {
      lat: 33.6981335731709,
      lng: 73.036895671875,
    },
    {
      lat: 33.684779099960515,
      lng: 73.04650870898438,
    },
    {
      lat: 33.693206228391965,
      lng: 73.06461898425293,
    },
    {
      lat: 33.706880699271096,
      lng: 73.05410472491455,
    },
  ]);
  const polygonRef = useRef();
  const listenersRef = useRef([]);
  const { error: errorQuery, loading: loadingQuery } = useQuery(
    GET_RESTAURANT_PROFILE,
    {
      variables: { id: restaurantId },
      fetchPolicy: "network-only",
      onCompleted,
      onError,
    }
  );
  const [mutate, { loading }] = useMutation(
    UPDATE_DELIVERY_BOUNDS_AND_LOCATION,
    {
      update: updateCache,
      onError,
      onCompleted,
    }
  );
  // Call setPath with new edited path
  const onEdit = useCallback(() => {
    if (polygonRef.current) {
      const nextPath = polygonRef.current
        .getPath()
        .getArray()
        .map((latLng) => {
          return { lat: latLng.lat(), lng: latLng.lng() };
        });
      setPath(nextPath);
    }
  }, [setPath]);

  const onLoadPolygon = useCallback(
    (polygon) => {
      polygonRef.current = polygon;
      const path = polygon.getPath();
      listenersRef.current.push(
        path.addListener("set_at", onEdit),
        path.addListener("insert_at", onEdit),
        path.addListener("remove_at", onEdit)
      );
    },
    [onEdit]
  );

  const onUnmount = useCallback(() => {
    listenersRef.current.forEach((lis) => lis.remove());
    polygonRef.current = null;
  }, []);

  const onClick = (e) => {
    if (drawBoundsOrMarker === "marker") {
      setMarker({ lat: e.latLng.lat(), lng: e.latLng.lng() });
    } else {
      setPath([...path, { lat: e.latLng.lat(), lng: e.latLng.lng() }]);
    }
  };

  const removePolygon = () => {
    setPath([]);
  };
  const removeMarker = () => {
    setMarker(null);
  };
  const toggleDrawingMode = (mode) => {
    setDrawBoundsOrMarker(mode);
  };

  // Helper functions
  const showSnackbar = useCallback((message, severity = "success") => {
    setSnackbar({
      open: true,
      message,
      severity,
    });
  }, []);

  const hideSnackbar = useCallback(() => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  }, []);

  function updateCache(cache, { data }) {
    if (data && data.result && data.result.data) {
      try {
        const { restaurant } = cache.readQuery({
          query: GET_RESTAURANT_PROFILE,
          variables: { id: restaurantId },
        });

        if (restaurant) {
          cache.writeQuery({
            query: GET_RESTAURANT_PROFILE,
            variables: { id: restaurantId },
            data: {
              restaurant: {
                ...restaurant,
                deliveryBounds: data.result.data.deliveryBounds,
                location: data.result.data.location,
              },
            },
          });
        }
      } catch (error) {
        console.error("Cache update error:", error);
      }
    }
  }

  function onCompleted(data) {
    // Handle query response
    if (data && data.restaurant) {
      const restaurant = data.restaurant;
      const newCenter = {
        lat: +restaurant.location.coordinates[1],
        lng: +restaurant.location.coordinates[0],
      };

      setCenter(newCenter);
      setMarker(newCenter);

      if (restaurant.deliveryBounds && restaurant.deliveryBounds.coordinates) {
        setPath(transformPolygon(restaurant.deliveryBounds.coordinates[0]));
      }

      setMapLoaded(true);
    }

    // Handle mutation response
    if (data && data.result) {
      if (data.result.success) {
        showSnackbar(t("LocationUpdatedSuccessfully"), "success");
      } else {
        showSnackbar(
          data.result.message || t("UpdatingLocationError"),
          "error"
        );
      }
    }
  }

  function onError(error) {
    console.error("Error:", error);
    let errorMsg = t("UpdatingLocationError");

    // Extract more specific error message if available
    if (error.graphQLErrors && error.graphQLErrors.length > 0) {
      errorMsg = error.graphQLErrors[0].message;
    } else if (error.networkError) {
      errorMsg = error.networkError.message;
    }

    showSnackbar(errorMsg, "error");
  }

  const validate = () => {
    if (!marker) {
      showSnackbar(t("LocationMarkerRequired"), "error");
      return false;
    }
    if (path.length < 3) {
      showSnackbar(t("DeliveryAreaRequired"), "error");
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validate()) return;

    const location = {
      latitude: marker.lat,
      longitude: marker.lng,
    };
    const bounds = transformPath(path);

    console.log("Sending mutation with variables:", {
      id: restaurantId,
      location,
      bounds,
      boundType: "Polygon",
    });

    try {
      await mutate({
        variables: {
          id: restaurantId,
          location,
          bounds,
          boundType: "Polygon",
        },
      });
    } catch (error) {
      console.error("Mutation error:", error);
    }
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setCenter(newLocation);
          setMarker(newLocation);
          showSnackbar(t("LocationUpdated"), "success");
        },
        (error) => {
          console.error("Geolocation error:", error);
          showSnackbar(t("GeolocationError"), "error");
        }
      );
    } else {
      showSnackbar(t("GeolocationNotSupported"), "error");
    }
  };

  const onDragEnd = (mapMouseEvent) => {
    setMarker({
      lat: mapMouseEvent.latLng.lat(),
      lng: mapMouseEvent.latLng.lng(),
    });
  };

  return (
    <>
      <Header />
      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 3,
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            color: "white",
            borderRadius: 3,
          }}
        >
          <Stack direction="row" alignItems="center" spacing={2}>
            <LocationOnIcon sx={{ fontSize: 32 }} />
            <Box>
              <Typography variant="h4" fontWeight="bold">
                {t("SetLocation")}
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                {t("SetLocationDescription")}
              </Typography>
            </Box>
          </Stack>
        </Paper>

        {loadingQuery && <CustomLoader />}
        {errorQuery && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {errorQuery.message}
          </Alert>
        )}

        <Stack spacing={3}>
          {/* Control Panel */}
          <Card elevation={2} sx={{ borderRadius: 3 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                {t("MapControls")}
              </Typography>

              <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
                <Chip
                  icon={<MyLocationIcon />}
                  label={t("SetRestaurantLocation")}
                  onClick={() => toggleDrawingMode("marker")}
                  color={
                    drawBoundsOrMarker === "marker" ? "primary" : "default"
                  }
                  variant={
                    drawBoundsOrMarker === "marker" ? "filled" : "outlined"
                  }
                  clickable
                />
                <Chip
                  icon={<PolygonIcon />}
                  label={t("DrawDeliveryBounds")}
                  onClick={() => toggleDrawingMode("polygon")}
                  color={
                    drawBoundsOrMarker === "polygon" ? "primary" : "default"
                  }
                  variant={
                    drawBoundsOrMarker === "polygon" ? "filled" : "outlined"
                  }
                  clickable
                />
                <Chip
                  icon={<MyLocationIcon />}
                  label={t("UseCurrentLocation")}
                  onClick={getCurrentLocation}
                  color="secondary"
                  variant="outlined"
                  clickable
                />
              </Stack>

              <Divider sx={{ my: 2 }} />

              <Stack direction="row" spacing={2}>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<ClearIcon />}
                  onClick={removePolygon}
                  size="small"
                >
                  {t("RemoveDeliveryBounds")}
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<ClearIcon />}
                  onClick={removeMarker}
                  size="small"
                >
                  {t("RemoveRestaurantLocation")}
                </Button>
              </Stack>
            </CardContent>
          </Card>

          {/* Map Section */}
          <Card elevation={3} sx={{ borderRadius: 3, overflow: "hidden" }}>
            <Box sx={{ position: "relative" }}>
              <GoogleMap
                mapContainerStyle={{
                  height: "600px",
                  width: "100%",
                }}
                id="google-map"
                zoom={14}
                center={center}
                onClick={onClick}
                options={{
                  styles: [
                    {
                      featureType: "all",
                      elementType: "geometry.fill",
                      stylers: [{ weight: "2.00" }],
                    },
                    {
                      featureType: "all",
                      elementType: "geometry.stroke",
                      stylers: [{ color: "#9c9c9c" }],
                    },
                    {
                      featureType: "all",
                      elementType: "labels.text",
                      stylers: [{ visibility: "on" }],
                    },
                  ],
                  disableDefaultUI: false,
                  zoomControl: true,
                  mapTypeControl: true,
                  scaleControl: true,
                  streetViewControl: true,
                  rotateControl: true,
                  fullscreenControl: true,
                }}
              >
                <Polygon
                  editable
                  draggable
                  onMouseUp={onEdit}
                  onDragEnd={onEdit}
                  onLoad={onLoadPolygon}
                  onUnmount={onUnmount}
                  onRightClick={removePolygon}
                  paths={path}
                  options={{
                    fillColor: theme.palette.primary.main,
                    fillOpacity: 0.2,
                    strokeColor: theme.palette.primary.main,
                    strokeOpacity: 1,
                    strokeWeight: 2,
                  }}
                />
                {marker && (
                  <Marker
                    position={marker}
                    draggable
                    onRightClick={removeMarker}
                    onDragEnd={onDragEnd}
                    icon={{
                      url:
                        "data:image/svg+xml;charset=UTF-8," +
                        encodeURIComponent(`
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="${theme.palette.error.main}" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                      `),
                      scaledSize: new window.google.maps.Size(32, 32),
                    }}
                  />
                )}
              </GoogleMap>

              {/* Floating Action Button */}
              <Fab
                color="primary"
                sx={{
                  position: "absolute",
                  bottom: 16,
                  right: 16,
                  boxShadow: 3,
                }}
                onClick={handleSave}
                disabled={loading}
              >
                {loading ? <CustomLoader size={24} /> : <SaveIcon />}
              </Fab>
            </Box>
          </Card>

          {/* Status Information */}
          {mapLoaded && (
            <Card elevation={1} sx={{ borderRadius: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom fontWeight="bold">
                  {t("Status")}
                </Typography>
                <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
                  <Chip
                    icon={marker ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={`${t("RestaurantLocation")}: ${
                      marker ? t("Set") : t("NotSet")
                    }`}
                    color={marker ? "success" : "error"}
                    variant="outlined"
                  />
                  <Chip
                    icon={
                      path.length >= 3 ? <CheckCircleIcon /> : <ErrorIcon />
                    }
                    label={`${t("DeliveryArea")}: ${
                      path.length >= 3 ? t("Set") : t("NotSet")
                    }`}
                    color={path.length >= 3 ? "success" : "error"}
                    variant="outlined"
                  />
                </Stack>
              </CardContent>
            </Card>
          )}
        </Stack>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={hideSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={hideSnackbar}
            severity={snackbar.severity}
            variant="filled"
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </>
  );
}

export default withTranslation()(DeliveryBoundsAndLocation);
